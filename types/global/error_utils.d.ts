// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

declare global {
    interface ErrorUtils {
        setGlobalHandler: (handler: (error: any, isFatal: boolean) => void) => void;
        getGlobalHandler: () => ((error: any, isFatal: boolean) => void) | undefined;
        reportError: (error: any) => void;
        reportFatalError: (error: any) => void;
    }

    var ErrorUtils: ErrorUtils;
    var __globalErrorHandler: ((error: any, isFatal: boolean) => void) | undefined;
}

export {};
